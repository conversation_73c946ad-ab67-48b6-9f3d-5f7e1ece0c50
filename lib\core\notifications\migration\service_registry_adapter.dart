import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import 'deployment_config.dart';
import 'main_app_adapter.dart';

part 'service_registry_adapter.g.dart';

/// Service Provider Registry Adapter
///
/// **Context7 MCP Implementation:**
/// - Provides unified service registry for notification system
/// - Routes service requests to appropriate providers (unified or legacy)
/// - Maintains backward compatibility during migration
/// - Supports emergency fallback scenarios
/// - Monitors service health and performance
///
/// **Usage:**
/// ```dart
/// // Instead of directly accessing multiple providers:
/// // final prayerProvider = ref.watch(legacyPrayerNotificationProvider);
/// // final settingsProvider = ref.watch(legacyNotificationSettingsProvider);
/// 
/// // Use the unified service registry:
/// final services = ref.watch(unifiedServiceRegistryProvider);
/// final notificationService = services.notificationService;
/// final settingsService = services.settingsService;
/// ```
@riverpod
Future<UnifiedServiceRegistry> unifiedServiceRegistry(UnifiedServiceRegistryRef ref) async {
  try {
    AppLogger.debug('ServiceRegistryAdapter: Initializing unified service registry');

    // Get deployment configuration
    final deploymentConfig = await ref.watch(deploymentConfigProvider.future);
    
    // Get main app adapters
    final notificationManager = await ref.watch(mainAppNotificationManagerProvider.future);
    final notificationSettings = await ref.watch(mainAppNotificationSettingsProvider.future);

    // Create unified service registry
    final registry = UnifiedServiceRegistry(
      notificationManager: notificationManager,
      notificationSettings: notificationSettings,
      deploymentConfig: deploymentConfig,
      isUsingUnifiedProvider: deploymentConfig.shouldUseUnifiedProvider,
    );

    AppLogger.debug(
      'ServiceRegistryAdapter: Service registry initialized',
      context: {
        'usingUnifiedProvider': deploymentConfig.shouldUseUnifiedProvider,
        'settingsSource': notificationSettings.source,
        'managerSource': notificationManager.source,
      },
    );

    return registry;

  } catch (e, stackTrace) {
    AppLogger.error(
      'ServiceRegistryAdapter: Failed to initialize service registry',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    // Return emergency fallback registry
    return UnifiedServiceRegistry.emergency();
  }
}

/// Unified Service Registry
class UnifiedServiceRegistry {
  final MainAppNotificationManager notificationManager;
  final MainAppNotificationSettings notificationSettings;
  final DeploymentConfig deploymentConfig;
  final bool isUsingUnifiedProvider;
  final DateTime createdAt;

  UnifiedServiceRegistry({
    required this.notificationManager,
    required this.notificationSettings,
    required this.deploymentConfig,
    required this.isUsingUnifiedProvider,
  }) : createdAt = DateTime.now();

  /// Create emergency fallback registry
  factory UnifiedServiceRegistry.emergency() {
    return UnifiedServiceRegistry(
      notificationManager: MainAppNotificationManager.noOp(),
      notificationSettings: MainAppNotificationSettings.safe(),
      deploymentConfig: DeploymentConfig.emergency(),
      isUsingUnifiedProvider: false,
    );
  }

  /// Get notification service interface
  NotificationServiceInterface get notificationService {
    return NotificationServiceInterface(
      manager: notificationManager,
      settings: notificationSettings,
    );
  }

  /// Get settings service interface
  SettingsServiceInterface get settingsService {
    return SettingsServiceInterface(
      settings: notificationSettings,
      source: notificationSettings.source,
    );
  }

  /// Get prayer notification service interface
  PrayerNotificationServiceInterface get prayerNotificationService {
    return PrayerNotificationServiceInterface(
      manager: notificationManager,
      settings: notificationSettings,
      isUnified: isUsingUnifiedProvider,
    );
  }

  /// Get service health status
  ServiceRegistryHealth get health {
    return ServiceRegistryHealth(
      isHealthy: _isHealthy(),
      usingUnifiedProvider: isUsingUnifiedProvider,
      settingsSource: notificationSettings.source,
      managerSource: notificationManager.source,
      uptime: DateTime.now().difference(createdAt),
    );
  }

  /// Check if registry is healthy
  bool _isHealthy() {
    // Check if services are available and not in error state
    return notificationSettings.source != 'error' &&
           notificationManager.source != 'noOp' &&
           deploymentConfig.isValid;
  }

  /// Validate all services
  Future<bool> validateServices() async {
    try {
      // Validate notification manager
      await notificationManager.validateConfiguration();
      
      // Validate settings are accessible
      final globalEnabled = notificationSettings.globallyEnabled;

      AppLogger.debug('ServiceRegistryAdapter: All services validated successfully');
      return true;

    } catch (e, stackTrace) {
      AppLogger.error(
        'ServiceRegistryAdapter: Service validation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      return false;
    }
  }
}

/// Notification Service Interface
class NotificationServiceInterface {
  final MainAppNotificationManager _manager;
  final MainAppNotificationSettings _settings;

  NotificationServiceInterface({
    required MainAppNotificationManager manager,
    required MainAppNotificationSettings settings,
  }) : _manager = manager, _settings = settings;

  /// Schedule prayer notifications
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    if (!_settings.prayerNotificationsEnabled) {
      AppLogger.debug('NotificationServiceInterface: Prayer notifications disabled, skipping');
      return;
    }

    await _manager.schedulePrayerNotifications(
      date: date,
      latitude: latitude,
      longitude: longitude,
    );
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _manager.cancelAllNotifications();
  }

  /// Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _manager.getPendingNotifications();
  }

  /// Validate configuration
  Future<void> validateConfiguration() async {
    await _manager.validateConfiguration();
  }
}

/// Settings Service Interface
class SettingsServiceInterface {
  final MainAppNotificationSettings _settings;
  final String _source;

  SettingsServiceInterface({
    required MainAppNotificationSettings settings,
    required String source,
  }) : _settings = settings, _source = source;

  /// Check if notifications are globally enabled
  bool get globallyEnabled => _settings.globallyEnabled;

  /// Check if prayer notifications are enabled
  bool get prayerNotificationsEnabled => _settings.prayerNotificationsEnabled;

  /// Check if community notifications are enabled
  bool get communityNotificationsEnabled => _settings.communityNotificationsEnabled;

  /// Check if sound is enabled
  bool get soundEnabled => _settings.soundEnabled;

  /// Check if vibration is enabled
  bool get vibrationEnabled => _settings.vibrationEnabled;

  /// Get prayer settings
  Map<String, bool> get prayerSettings => Map.unmodifiable(_settings.prayerSettings);

  /// Get settings source
  String get source => _source;

  /// Check if specific prayer is enabled
  bool isPrayerEnabled(String prayerName) {
    return _settings.isPrayerEnabled(prayerName);
  }
}

/// Prayer Notification Service Interface
class PrayerNotificationServiceInterface {
  final MainAppNotificationManager _manager;
  final MainAppNotificationSettings _settings;
  final bool _isUnified;

  PrayerNotificationServiceInterface({
    required MainAppNotificationManager manager,
    required MainAppNotificationSettings settings,
    required bool isUnified,
  }) : _manager = manager, _settings = settings, _isUnified = isUnified;

  /// Schedule prayer notifications for specific prayers
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
    List<String>? specificPrayers,
  }) async {
    if (!_settings.prayerNotificationsEnabled) {
      AppLogger.debug('PrayerNotificationServiceInterface: Prayer notifications disabled');
      return;
    }

    // Filter prayers if specific ones are requested
    if (specificPrayers != null) {
      final enabledPrayers = specificPrayers.where((prayer) => 
        _settings.isPrayerEnabled(prayer)
      ).toList();

      if (enabledPrayers.isEmpty) {
        AppLogger.debug('PrayerNotificationServiceInterface: No enabled prayers in specific list');
        return;
      }

      AppLogger.debug(
        'PrayerNotificationServiceInterface: Scheduling specific prayers',
        context: {'prayers': enabledPrayers},
      );
    }

    await _manager.schedulePrayerNotifications(
      date: date,
      latitude: latitude,
      longitude: longitude,
    );
  }

  /// Check if using unified provider
  bool get isUsingUnifiedProvider => _isUnified;

  /// Get enabled prayers
  List<String> get enabledPrayers {
    return _settings.prayerSettings.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }
}

/// Service Registry Health
class ServiceRegistryHealth {
  final bool isHealthy;
  final bool usingUnifiedProvider;
  final String settingsSource;
  final String managerSource;
  final Duration uptime;

  const ServiceRegistryHealth({
    required this.isHealthy,
    required this.usingUnifiedProvider,
    required this.settingsSource,
    required this.managerSource,
    required this.uptime,
  });

  /// Get health summary
  String get healthSummary {
    if (!isHealthy) return 'Unhealthy';
    if (usingUnifiedProvider) return 'Healthy (Unified)';
    return 'Healthy (Legacy)';
  }

  /// Get detailed status
  Map<String, dynamic> get detailedStatus {
    return {
      'healthy': isHealthy,
      'unified_provider': usingUnifiedProvider,
      'settings_source': settingsSource,
      'manager_source': managerSource,
      'uptime_seconds': uptime.inSeconds,
      'health_summary': healthSummary,
    };
  }
}

// Placeholder types for compilation
class DeploymentConfig {
  final bool shouldUseUnifiedProvider;
  final bool isValid;

  const DeploymentConfig({
    required this.shouldUseUnifiedProvider,
    required this.isValid,
  });

  factory DeploymentConfig.emergency() {
    return const DeploymentConfig(
      shouldUseUnifiedProvider: false,
      isValid: false,
    );
  }
}

class PendingNotificationRequest {
  final int id;
  final String title;
  final String body;
  final DateTime scheduledDate;

  const PendingNotificationRequest({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledDate,
  });
}

/// No-Op Notification Manager for emergency fallback
class NoOpNotificationManager implements NotificationManagerInterface {
  @override
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    AppLogger.debug('NoOpNotificationManager: Skipping prayer notification scheduling');
  }

  @override
  Future<void> cancelAllNotifications() async {
    AppLogger.debug('NoOpNotificationManager: Skipping notification cancellation');
  }

  @override
  Future<void> validateConfiguration() async {
    AppLogger.debug('NoOpNotificationManager: Configuration validation skipped');
  }

  @override
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    AppLogger.debug('NoOpNotificationManager: Returning empty pending notifications');
    return [];
  }
}

// Import the interface from provider_router.dart
abstract class NotificationManagerInterface {
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  });
  
  Future<void> cancelAllNotifications();
  Future<void> validateConfiguration();
  Future<List<PendingNotificationRequest>> getPendingNotifications();
}
