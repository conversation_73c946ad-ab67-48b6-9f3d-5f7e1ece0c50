import 'dart:async';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../features/auth/domain/entities/user.dart' as domain;
import '../../errors/app_error.dart';
import '../../utils/logger.dart';
import '../../utils/result.dart';
import '../models/auth_state.dart';
import '../models/security_context.dart';
import 'secure_token_manager.dart';

/// Session Manager following Context7 MCP best practices
///
/// Provides comprehensive session lifecycle management with security monitoring,
/// device trust evaluation, risk assessment, and automatic session cleanup.
///
/// Consolidates all session-related functionality into a single service.
abstract class SessionManager {
  /// Create a new authenticated session
  Future<Result<SecurityContext>> createSession(domain.User user, Session supabaseSession, DeviceInfo? deviceInfo);

  /// Validate an existing session
  Future<Result<SecurityContext>> validateSession(String sessionId);

  /// Refresh session and extend expiry
  Future<Result<SecurityContext>> refreshSession(String sessionId);

  /// Terminate a specific session
  Future<Result<void>> terminateSession(String sessionId);

  /// Terminate all sessions for a user
  Future<Result<void>> terminateAllSessions(String userId);

  /// Get current session information
  Future<Result<SecurityContext?>> getCurrentSession();

  /// Update session activity
  Future<Result<void>> updateSessionActivity(String sessionId);

  /// Cleanup expired sessions
  Future<Result<int>> cleanupExpiredSessions();

  /// Get session security metrics
  Future<Result<SessionSecurityMetrics>> getSessionMetrics(String sessionId);

  /// Assess session risk
  Future<Result<RiskAssessment>> assessSessionRisk(String sessionId);

  /// Monitor session for suspicious activity
  Stream<SecurityEvent> monitorSession(String sessionId);
}

/// Implementation of SessionManager with enterprise security features
class SessionManagerImpl implements SessionManager {
  final SecureTokenManager _tokenManager;
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final StreamController<SecurityEvent> _securityEventController = StreamController<SecurityEvent>.broadcast();

  // In-memory session cache for performance
  final Map<String, SecurityContext> _sessionCache = {};
  final Map<String, Timer> _sessionTimers = {};

  SessionManagerImpl({required SecureTokenManager tokenManager}) : _tokenManager = tokenManager;

  @override
  Future<Result<SecurityContext>> createSession(
    domain.User user,
    Session supabaseSession,
    DeviceInfo? deviceInfo,
  ) async {
    try {
      AppLogger.info('Creating new authenticated session for user: ${user.id}');

      // Generate session ID
      final sessionId = _generateSessionId();
      final now = DateTime.now();

      // Get device information
      final deviceTrust = await _evaluateDeviceTrust(deviceInfo);

      // Determine assurance level based on authentication method
      final assuranceLevel = _determineAssuranceLevel(supabaseSession);

      // Create security context
      final securityContext = SecurityContext(
        sessionId: sessionId,
        userId: user.id,
        createdAt: now,
        lastActivity: now,
        expiresAt: now.add(const Duration(hours: 8)), // 8-hour session
        deviceTrust: deviceTrust,
        assuranceLevel: assuranceLevel,
        permissions: _getUserPermissions(user),
        roles: _getUserRoles(user),
        ipAddress: await _getCurrentIpAddress(),
        userAgent: await _getUserAgent(),
        deviceInfo: deviceInfo != null
            ? {
                'platform': deviceInfo.platform,
                'osVersion': deviceInfo.osVersion,
                'deviceModel': deviceInfo.deviceModel,
                'manufacturer': deviceInfo.manufacturer,
                'appVersion': deviceInfo.appVersion,
              }
            : null,
        securityFlags: [],
        riskAssessment: await _performInitialRiskAssessment(user, deviceTrust),
      );

      // Store session information
      final sessionInfo = SessionInfo(
        sessionId: sessionId,
        userId: user.id,
        createdAt: now,
        lastActivity: now,
        expiresAt: securityContext.expiresAt,
        deviceId: deviceTrust.deviceId,
        deviceName: deviceInfo?.deviceModel,
        ipAddress: securityContext.ipAddress,
        userAgent: securityContext.userAgent,
      );

      await _tokenManager.storeSessionInfo(sessionInfo);

      // Cache session
      _sessionCache[sessionId] = securityContext;

      // Set up session expiry timer
      _setupSessionTimer(sessionId, securityContext.expiresAt);

      // Log security event
      await _logSecurityEvent(
        SecurityEvent(
          id: _generateEventId(),
          type: SecurityEventType.sessionCreated,
          timestamp: now,
          sessionId: sessionId,
          userId: user.id,
          deviceId: deviceTrust.deviceId,
          ipAddress: securityContext.ipAddress,
          userAgent: securityContext.userAgent,
          description: 'New session created',
        ),
      );

      AppLogger.info('Session created successfully: $sessionId');
      return Result.success(securityContext);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to create session', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to create session', originalError: e.toString()));
    }
  }

  @override
  Future<Result<SecurityContext>> validateSession(String sessionId) async {
    try {
      AppLogger.debug('Validating session: $sessionId');

      // Check cache first
      if (_sessionCache.containsKey(sessionId)) {
        final cachedSession = _sessionCache[sessionId]!;
        if (cachedSession.isValid) {
          return Result.success(cachedSession);
        } else {
          // Remove expired session from cache
          _sessionCache.remove(sessionId);
          _sessionTimers[sessionId]?.cancel();
          _sessionTimers.remove(sessionId);
        }
      }

      // Load session from storage
      final sessionResult = await _tokenManager.getSessionInfo();
      if (sessionResult.isFailure || sessionResult.valueOrNull == null) {
        return Result.failure(AppError.authentication('Session not found or invalid'));
      }

      final sessionInfo = sessionResult.valueOrNull!;

      // Check if session is expired
      if (sessionInfo.expiresAt.isBefore(DateTime.now())) {
        await _logSecurityEvent(
          SecurityEvent(
            id: _generateEventId(),
            type: SecurityEventType.sessionExpired,
            timestamp: DateTime.now(),
            sessionId: sessionId,
            userId: sessionInfo.userId,
            description: 'Session expired during validation',
          ),
        );

        return Result.failure(
          AppError.authentication('Session expired', originalError: 'Session has expired and needs renewal'),
        );
      }

      // Reconstruct security context
      final securityContext = await _reconstructSecurityContext(sessionInfo);

      // Update cache
      _sessionCache[sessionId] = securityContext;

      AppLogger.debug('Session validated successfully: $sessionId');
      return Result.success(securityContext);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to validate session', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to validate session', originalError: e.toString()));
    }
  }

  @override
  Future<Result<SecurityContext>> refreshSession(String sessionId) async {
    try {
      AppLogger.info('Refreshing session: $sessionId');

      final validationResult = await validateSession(sessionId);
      if (validationResult.isFailure) {
        return validationResult;
      }

      final currentContext = validationResult.valueOrNull!;
      final now = DateTime.now();

      // Extend session expiry
      final newExpiryTime = now.add(const Duration(hours: 8));

      // Update security context
      final refreshedContext = currentContext.copyWith(lastActivity: now, expiresAt: newExpiryTime);

      // Update stored session info
      final sessionInfo = SessionInfo(
        sessionId: sessionId,
        userId: currentContext.userId,
        createdAt: currentContext.createdAt,
        lastActivity: now,
        expiresAt: newExpiryTime,
        deviceId: currentContext.deviceTrust.deviceId,
        ipAddress: currentContext.ipAddress,
        userAgent: currentContext.userAgent,
      );

      await _tokenManager.storeSessionInfo(sessionInfo);

      // Update cache
      _sessionCache[sessionId] = refreshedContext;

      // Reset session timer
      _setupSessionTimer(sessionId, newExpiryTime);

      // Log security event
      await _logSecurityEvent(
        SecurityEvent(
          id: _generateEventId(),
          type: SecurityEventType.sessionCreated, // Using created for refresh
          timestamp: now,
          sessionId: sessionId,
          userId: currentContext.userId,
          description: 'Session refreshed',
        ),
      );

      AppLogger.info('Session refreshed successfully: $sessionId');
      return Result.success(refreshedContext);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to refresh session', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to refresh session', originalError: e.toString()));
    }
  }

  @override
  Future<Result<void>> terminateSession(String sessionId) async {
    try {
      AppLogger.info('Terminating session: $sessionId');

      // Get session info for logging
      final sessionResult = await _tokenManager.getSessionInfo();

      // Clear session data
      await _tokenManager.clearSessionInfo();
      await _tokenManager.clearTokens();

      // Remove from cache
      _sessionCache.remove(sessionId);
      _sessionTimers[sessionId]?.cancel();
      _sessionTimers.remove(sessionId);

      // Log security event
      if (sessionResult.isSuccess && sessionResult.valueOrNull != null) {
        await _logSecurityEvent(
          SecurityEvent(
            id: _generateEventId(),
            type: SecurityEventType.sessionTerminated,
            timestamp: DateTime.now(),
            sessionId: sessionId,
            userId: sessionResult.valueOrNull!.userId,
            description: 'Session terminated',
          ),
        );
      }

      AppLogger.info('Session terminated successfully: $sessionId');
      return const Result.success(null);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to terminate session', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to terminate session', originalError: e.toString()));
    }
  }

  @override
  Future<Result<void>> terminateAllSessions(String userId) async {
    try {
      AppLogger.info('Terminating all sessions for user: $userId');

      // In a full implementation, this would query all sessions for the user
      // For now, we'll just clear the current session
      await terminateSession('current');

      AppLogger.info('All sessions terminated for user: $userId');
      return const Result.success(null);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to terminate all sessions', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to terminate all sessions', originalError: e.toString()));
    }
  }

  @override
  Future<Result<SecurityContext?>> getCurrentSession() async {
    try {
      final sessionResult = await _tokenManager.getSessionInfo();
      if (sessionResult.isFailure || sessionResult.valueOrNull == null) {
        return const Result.success(null);
      }

      final sessionInfo = sessionResult.valueOrNull!;
      final securityContext = await _reconstructSecurityContext(sessionInfo);

      return Result.success(securityContext);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to get current session', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to get current session', originalError: e.toString()));
    }
  }

  @override
  Future<Result<void>> updateSessionActivity(String sessionId) async {
    try {
      final sessionResult = await validateSession(sessionId);
      if (sessionResult.isFailure) {
        return Result.failure(sessionResult.errorOrNull!);
      }

      final context = sessionResult.valueOrNull!;
      final now = DateTime.now();

      // Update last activity
      final updatedContext = context.copyWith(lastActivity: now);
      _sessionCache[sessionId] = updatedContext;

      // Update stored session info
      final sessionInfo = SessionInfo(
        sessionId: sessionId,
        userId: context.userId,
        createdAt: context.createdAt,
        lastActivity: now,
        expiresAt: context.expiresAt,
        deviceId: context.deviceTrust.deviceId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      );

      await _tokenManager.storeSessionInfo(sessionInfo);

      return const Result.success(null);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to update session activity', e, stackTrace);
      return Result.failure(AppError.authentication('Failed to update session activity', originalError: e.toString()));
    }
  }

  @override
  Future<Result<int>> cleanupExpiredSessions() async {
    try {
      AppLogger.info('Cleaning up expired sessions');

      var cleanedCount = 0;
      final now = DateTime.now();

      // Clean up cached sessions
      final expiredSessions = _sessionCache.entries
          .where((entry) => entry.value.expiresAt.isBefore(now))
          .map((entry) => entry.key)
          .toList();

      for (final sessionId in expiredSessions) {
        _sessionCache.remove(sessionId);
        _sessionTimers[sessionId]?.cancel();
        _sessionTimers.remove(sessionId);
        cleanedCount++;
      }

      AppLogger.info('Cleaned up $cleanedCount expired sessions');
      return Result.success(cleanedCount);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to cleanup expired sessions', e, stackTrace);
      return Result.failure(AppError.unknown('Failed to cleanup expired sessions', originalError: e.toString()));
    }
  }

  @override
  Future<Result<SessionSecurityMetrics>> getSessionMetrics(String sessionId) async {
    // Placeholder implementation
    // In a full implementation, this would gather comprehensive metrics
    return Result.success(
      SessionSecurityMetrics(
        totalRequests: 0,
        failedAttempts: 0,
        firstActivity: DateTime.now(),
        lastActivity: DateTime.now(),
        accessedResources: [],
        ipAddresses: [],
        userAgents: [],
      ),
    );
  }

  @override
  Future<Result<RiskAssessment>> assessSessionRisk(String sessionId) async {
    // Placeholder implementation
    // In a full implementation, this would perform comprehensive risk analysis
    return Result.success(
      RiskAssessment(overallRisk: RiskLevel.low, riskScore: 25.0, assessedAt: DateTime.now(), riskFactors: []),
    );
  }

  @override
  Stream<SecurityEvent> monitorSession(String sessionId) {
    return _securityEventController.stream.where((event) => event.sessionId == sessionId);
  }

  // Private helper methods

  String _generateSessionId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond % 1000}';
  }

  String _generateEventId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  Future<DeviceTrust> _evaluateDeviceTrust(DeviceInfo? deviceInfo) async {
    // Simplified device trust evaluation
    return DeviceTrust(
      deviceId: deviceInfo?.deviceId ?? 'unknown',
      deviceFingerprint: _generateDeviceFingerprint(deviceInfo),
      trustLevel: TrustLevel.basic,
      lastSeen: DateTime.now(),
      registeredAt: DateTime.now(),
    );
  }

  String _generateDeviceFingerprint(DeviceInfo? deviceInfo) {
    if (deviceInfo == null) return 'unknown';

    final data = '${deviceInfo.platform}_${deviceInfo.osVersion}_${deviceInfo.deviceModel}';
    return data.hashCode.toString();
  }

  AssuranceLevel _determineAssuranceLevel(Session session) {
    // Simplified assurance level determination
    // In a real implementation, this would consider MFA, device trust, etc.
    return AssuranceLevel.medium;
  }

  List<String> _getUserPermissions(domain.User user) {
    // Simplified permission assignment
    return ['read', 'write'];
  }

  List<String> _getUserRoles(domain.User user) {
    // Simplified role assignment
    return ['user'];
  }

  Future<String?> _getCurrentIpAddress() async {
    // Placeholder - would get actual IP address
    return '127.0.0.1';
  }

  Future<String?> _getUserAgent() async {
    // Placeholder - would get actual user agent
    return 'Flutter App';
  }

  Future<RiskAssessment> _performInitialRiskAssessment(domain.User user, DeviceTrust deviceTrust) async {
    // Simplified risk assessment
    return RiskAssessment(overallRisk: RiskLevel.low, riskScore: 20.0, assessedAt: DateTime.now(), riskFactors: []);
  }

  Future<SecurityContext> _reconstructSecurityContext(SessionInfo sessionInfo) async {
    return SecurityContext(
      sessionId: sessionInfo.sessionId,
      userId: sessionInfo.userId,
      createdAt: sessionInfo.createdAt,
      lastActivity: sessionInfo.lastActivity,
      expiresAt: sessionInfo.expiresAt,
      deviceTrust: DeviceTrust(
        deviceId: sessionInfo.deviceId ?? 'unknown',
        deviceFingerprint: 'reconstructed',
        trustLevel: TrustLevel.basic,
        lastSeen: DateTime.now(),
      ),
      assuranceLevel: AssuranceLevel.medium,
      permissions: ['read', 'write'],
      roles: ['user'],
      ipAddress: sessionInfo.ipAddress,
      userAgent: sessionInfo.userAgent,
    );
  }

  void _setupSessionTimer(String sessionId, DateTime expiryTime) {
    _sessionTimers[sessionId]?.cancel();

    final duration = expiryTime.difference(DateTime.now());
    if (duration.isNegative) return;

    _sessionTimers[sessionId] = Timer(duration, () {
      _sessionCache.remove(sessionId);
      _sessionTimers.remove(sessionId);

      _logSecurityEvent(
        SecurityEvent(
          id: _generateEventId(),
          type: SecurityEventType.sessionExpired,
          timestamp: DateTime.now(),
          sessionId: sessionId,
          description: 'Session expired automatically',
        ),
      );
    });
  }

  Future<void> _logSecurityEvent(SecurityEvent event) async {
    AppLogger.info('Security event: ${event.type.name} - ${event.description}');
    _securityEventController.add(event);
  }

  void dispose() {
    _securityEventController.close();
    for (final timer in _sessionTimers.values) {
      timer.cancel();
    }
    _sessionTimers.clear();
    _sessionCache.clear();
  }
}
