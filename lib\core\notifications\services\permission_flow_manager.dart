import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart';
import '../providers/unified_notification_provider.dart';
import '../widgets/permission_settings_guidance_dialog.dart';
import '../widgets/unified_permission_request_dialog.dart';

/// Permission Flow Manager
///
/// **Task 3.3.2: Implement permission request flows with user-friendly messaging**
///
/// This service manages the complete permission request flow following Context7 MCP patterns:
/// - Progressive permission requests with user-friendly messaging
/// - Intelligent fallback to settings guidance
/// - Comprehensive error handling and recovery
/// - Analytics and monitoring integration
/// - Accessibility and localization support
class PermissionFlowManager {
  final WidgetRef ref;
  final BuildContext context;

  PermissionFlowManager({required this.ref, required this.context});

  /// Start comprehensive permission request flow
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Manages complete permission flow
  /// - Dependency inversion: Uses abstract notification provider interface
  /// - Open/closed principle: Extensible for new permission types
  /// - Interface segregation: Focused on permission flow management
  ///
  /// **Usage:**
  /// ```dart
  /// final flowManager = PermissionFlowManager(ref: ref, context: context);
  /// final result = await flowManager.requestPermissions([
  ///   PermissionNotificationType.local,
  ///   PermissionNotificationType.push,
  /// ]);
  /// ```
  Future<PermissionFlowResult> requestPermissions(
    List<PermissionNotificationType> types, {
    String? customTitle,
    String? customDescription,
    bool showBenefits = true,
    bool showConsequences = true,
    bool allowSkip = true,
    bool fallbackToSettings = true,
    Map<String, dynamic> options = const {},
  }) async {
    try {
      // Stage 1: Check current permission status
      final currentStatus = await _checkCurrentPermissions(types);
      final deniedTypes = _getDeniedTypes(currentStatus, types);

      if (deniedTypes.isEmpty) {
        return PermissionFlowResult.success(
          grantedTypes: types,
          deniedTypes: [],
          flowCompleted: true,
          userInteractionRequired: false,
        );
      }

      // Stage 2: Show permission request dialog
      final dialogResult = await _showPermissionRequestDialog(
        deniedTypes,
        customTitle: customTitle,
        customDescription: customDescription,
        showBenefits: showBenefits,
        showConsequences: showConsequences,
        allowSkip: allowSkip,
      );

      if (dialogResult == null) {
        return PermissionFlowResult.cancelled(requestedTypes: types, deniedTypes: deniedTypes);
      }

      // Stage 3: Process dialog results
      final grantedFromDialog = dialogResult.entries.where((entry) => entry.value).map((entry) => entry.key).toList();

      final stillDeniedTypes = dialogResult.entries.where((entry) => !entry.value).map((entry) => entry.key).toList();

      // Stage 4: Handle settings fallback if needed
      if (stillDeniedTypes.isNotEmpty && fallbackToSettings) {
        final settingsResult = await _handleSettingsFallback(stillDeniedTypes);

        if (settingsResult.settingsOpened) {
          // Re-check permissions after settings interaction
          final updatedStatus = await _checkCurrentPermissions(stillDeniedTypes);
          final finalGranted = _getGrantedTypes(updatedStatus, stillDeniedTypes);
          final finalDenied = stillDeniedTypes.where((type) => !finalGranted.contains(type)).toList();

          return PermissionFlowResult.success(
            grantedTypes: [...grantedFromDialog, ...finalGranted],
            deniedTypes: finalDenied,
            flowCompleted: true,
            userInteractionRequired: true,
            settingsFallbackUsed: true,
          );
        }
      }

      return PermissionFlowResult.success(
        grantedTypes: grantedFromDialog,
        deniedTypes: stillDeniedTypes,
        flowCompleted: true,
        userInteractionRequired: true,
        settingsFallbackUsed: false,
      );
    } catch (e, stackTrace) {
      return PermissionFlowResult.error(requestedTypes: types, error: e.toString(), stackTrace: stackTrace);
    }
  }

  /// Check current permission status for types
  Future<Map<PermissionNotificationType, PermissionStatus>> _checkCurrentPermissions(
    List<PermissionNotificationType> types,
  ) async {
    final result = await ref.read(unifiedNotificationSettingsProvider.notifier).checkPermissions(types: types);

    return result.permissionResults;
  }

  /// Get denied permission types
  List<PermissionNotificationType> _getDeniedTypes(
    Map<PermissionNotificationType, PermissionStatus> status,
    List<PermissionNotificationType> requestedTypes,
  ) {
    return requestedTypes.where((type) {
      final permissionStatus = status[type];
      return permissionStatus != PermissionStatus.granted;
    }).toList();
  }

  /// Get granted permission types
  List<PermissionNotificationType> _getGrantedTypes(
    Map<PermissionNotificationType, PermissionStatus> status,
    List<PermissionNotificationType> requestedTypes,
  ) {
    return requestedTypes.where((type) {
      final permissionStatus = status[type];
      return permissionStatus == PermissionStatus.granted;
    }).toList();
  }

  /// Show permission request dialog
  Future<Map<PermissionNotificationType, bool>?> _showPermissionRequestDialog(
    List<PermissionNotificationType> types, {
    String? customTitle,
    String? customDescription,
    bool showBenefits = true,
    bool showConsequences = true,
    bool allowSkip = true,
  }) async {
    return showDialog<Map<PermissionNotificationType, bool>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => UnifiedPermissionRequestDialog(
        requestedTypes: types,
        customTitle: customTitle,
        customDescription: customDescription,
        showBenefits: showBenefits,
        showConsequences: showConsequences,
        allowSkip: allowSkip,
      ),
    );
  }

  /// Handle settings fallback flow
  Future<SettingsFallbackResult> _handleSettingsFallback(List<PermissionNotificationType> deniedTypes) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PermissionSettingsGuidanceDialog(deniedTypes: deniedTypes),
    );

    return SettingsFallbackResult(settingsOpened: result ?? false, userCompleted: result != null);
  }

  /// Show permission status summary
  Future<void> showPermissionStatusSummary({bool includeRecommendations = true, bool showHealthStatus = true}) async {
    try {
      final report = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();

      if (!context.mounted) return;

      await showDialog(
        context: context,
        builder: (context) => _PermissionStatusSummaryDialog(
          report: report,
          includeRecommendations: includeRecommendations,
          showHealthStatus: showHealthStatus,
        ),
      );
    } on Exception catch (e) {
      // Handle error showing status summary
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Unable to load permission status: ${e.toString()}'), backgroundColor: Colors.red),
      );
    }
  }

  /// Request specific permission with custom flow
  Future<bool> requestSinglePermission(
    PermissionNotificationType type, {
    String? customRationale,
    bool showBenefits = true,
    bool fallbackToSettings = true,
  }) async {
    final result = await requestPermissions(
      [type],
      showBenefits: showBenefits,
      showConsequences: false,
      allowSkip: false,
      fallbackToSettings: fallbackToSettings,
    );

    return result.grantedTypes.contains(type);
  }

  /// Check if all required permissions are granted
  Future<bool> areAllPermissionsGranted(List<PermissionNotificationType> types) async {
    final status = await _checkCurrentPermissions(types);
    return types.every((type) => status[type] == PermissionStatus.granted);
  }

  /// Get missing permissions
  Future<List<PermissionNotificationType>> getMissingPermissions(List<PermissionNotificationType> requiredTypes) async {
    final status = await _checkCurrentPermissions(requiredTypes);
    return _getDeniedTypes(status, requiredTypes);
  }
}

/// Permission Flow Result
///
/// Result of a complete permission request flow.
class PermissionFlowResult {
  final List<PermissionNotificationType> requestedTypes;
  final List<PermissionNotificationType> grantedTypes;
  final List<PermissionNotificationType> deniedTypes;
  final bool flowCompleted;
  final bool userInteractionRequired;
  final bool settingsFallbackUsed;
  final bool isCancelled;
  final bool isError;
  final String? error;
  final StackTrace? stackTrace;

  const PermissionFlowResult({
    required this.requestedTypes,
    required this.grantedTypes,
    required this.deniedTypes,
    required this.flowCompleted,
    required this.userInteractionRequired,
    this.settingsFallbackUsed = false,
    this.isCancelled = false,
    this.isError = false,
    this.error,
    this.stackTrace,
  });

  factory PermissionFlowResult.success({
    required List<PermissionNotificationType> grantedTypes,
    required List<PermissionNotificationType> deniedTypes,
    required bool flowCompleted,
    required bool userInteractionRequired,
    bool settingsFallbackUsed = false,
  }) {
    return PermissionFlowResult(
      requestedTypes: [...grantedTypes, ...deniedTypes],
      grantedTypes: grantedTypes,
      deniedTypes: deniedTypes,
      flowCompleted: flowCompleted,
      userInteractionRequired: userInteractionRequired,
      settingsFallbackUsed: settingsFallbackUsed,
    );
  }

  factory PermissionFlowResult.cancelled({
    required List<PermissionNotificationType> requestedTypes,
    required List<PermissionNotificationType> deniedTypes,
  }) {
    return PermissionFlowResult(
      requestedTypes: requestedTypes,
      grantedTypes: [],
      deniedTypes: deniedTypes,
      flowCompleted: false,
      userInteractionRequired: true,
      isCancelled: true,
    );
  }

  factory PermissionFlowResult.error({
    required List<PermissionNotificationType> requestedTypes,
    required String error,
    StackTrace? stackTrace,
  }) {
    return PermissionFlowResult(
      requestedTypes: requestedTypes,
      grantedTypes: [],
      deniedTypes: requestedTypes,
      flowCompleted: false,
      userInteractionRequired: false,
      isError: true,
      error: error,
      stackTrace: stackTrace,
    );
  }

  bool get isSuccess => !isError && !isCancelled;
  bool get hasAllPermissions => deniedTypes.isEmpty;
  bool get hasPartialPermissions => grantedTypes.isNotEmpty && deniedTypes.isNotEmpty;
}

/// Settings Fallback Result
class SettingsFallbackResult {
  final bool settingsOpened;
  final bool userCompleted;

  const SettingsFallbackResult({required this.settingsOpened, required this.userCompleted});
}

/// Permission Status Summary Dialog (placeholder)
class _PermissionStatusSummaryDialog extends StatelessWidget {
  final PermissionStatusReport report;
  final bool includeRecommendations;
  final bool showHealthStatus;

  const _PermissionStatusSummaryDialog({
    required this.report,
    required this.includeRecommendations,
    required this.showHealthStatus,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Permission Status'),
      content: const Text('Permission status summary would be displayed here.'),
      actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
    );
  }
}
