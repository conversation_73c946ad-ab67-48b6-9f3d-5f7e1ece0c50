import 'dart:io';

import 'package:flutter/foundation.dart';

/// Migration Utilities
///
/// **Task 4.1.3: Add migration utilities for automated code updates**
///
/// This utility class provides automated code migration tools following Context7 MCP
/// patterns for seamless provider migration and code transformation.
///
/// Features:
/// - Automated code scanning and transformation
/// - Provider import statement updates
/// - Method call replacements with proper mapping
/// - Batch file processing with progress tracking
/// - Backup creation before modifications
/// - Rollback capabilities for failed migrations
/// - Validation and testing integration
/// - Migration report generation
/// - Context7 MCP compliance verification
/// - Developer-friendly migration scripts
class MigrationUtilities {
  static final MigrationUtilities _instance = MigrationUtilities._internal();
  factory MigrationUtilities() => _instance;
  MigrationUtilities._internal();

  /// Migration patterns for automated code transformation
  static const Map<String, MigrationPattern> _migrationPatterns = {
    // Legacy Prayer Notification Provider patterns
    'legacyPrayerNotificationProvider': MigrationPattern(
      oldImport:
          "import 'package:masajid_albahrain/core/notifications/providers/legacy_prayer_notification_provider.dart';",
      newImport: "import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';",
      providerReplacements: {'legacyPrayerNotificationProvider': 'unifiedNotificationSettingsProvider'},
      methodReplacements: {
        'enableNotifications()': 'updateGlobalSettings(globallyEnabled: true)',
        'disableNotifications()': 'updateGlobalSettings(globallyEnabled: false)',
        'setPrayerNotification(\'fajr\', true)': 'updatePrayerSettings(PrayerType.fajr, enabled: true)',
        'setPrayerNotification(\'dhuhr\', true)': 'updatePrayerSettings(PrayerType.dhuhr, enabled: true)',
        'setPrayerNotification(\'asr\', true)': 'updatePrayerSettings(PrayerType.asr, enabled: true)',
        'setPrayerNotification(\'maghrib\', true)': 'updatePrayerSettings(PrayerType.maghrib, enabled: true)',
        'setPrayerNotification(\'isha\', true)': 'updatePrayerSettings(PrayerType.isha, enabled: true)',
        'setSoundEnabled(': 'updateSoundSettings(enabled: ',
        'setVibrationEnabled(': 'updateVibrationSettings(enabled: ',
        'setReminderMinutes(': 'updateReminderSettings(defaultMinutesBefore: ',
      },
    ),

    // Legacy Community Notification Provider patterns
    'legacyCommunityNotificationProvider': MigrationPattern(
      oldImport:
          "import 'package:masajid_albahrain/core/notifications/providers/legacy_community_notification_provider.dart';",
      newImport: "import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';",
      providerReplacements: {'legacyCommunityNotificationProvider': 'unifiedNotificationSettingsProvider'},
      methodReplacements: {
        'enableCommunityNotifications()': 'updateCommunitySettings(enabled: true)',
        'disableCommunityNotifications()': 'updateCommunitySettings(enabled: false)',
        'setAnnouncementsEnabled(': 'updateCommunitySettings(announcements: ',
        'setEventsEnabled(': 'updateCommunitySettings(events: ',
        'setNewsEnabled(': 'updateCommunitySettings(news: ',
      },
    ),
  };

  /// Scan project for legacy provider usage
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Focused on code scanning and analysis
  /// - Open/closed principle: Extensible for new provider patterns
  /// - Dependency inversion: Uses abstract scanning interfaces
  /// - Interface segregation: Specific scanning methods for different file types
  ///
  /// **Usage:**
  /// ```dart
  /// final scanResult = await MigrationUtilities().scanProject();
  /// print('Found ${scanResult.legacyUsages.length} legacy usages');
  /// ```
  Future<MigrationScanResult> scanProject({
    String projectPath = '.',
    List<String> includePaths = const ['lib/'],
    List<String> excludePaths = const ['.dart_tool/', 'build/'],
  }) async {
    final legacyUsages = <LegacyUsage>[];
    final filesToMigrate = <String>[];

    try {
      // Scan all Dart files in the project
      final dartFiles = await _findDartFiles(projectPath, includePaths, excludePaths);

      for (final filePath in dartFiles) {
        final fileContent = await File(filePath).readAsString();
        final usages = _scanFileForLegacyUsage(filePath, fileContent);

        if (usages.isNotEmpty) {
          legacyUsages.addAll(usages);
          filesToMigrate.add(filePath);
        }
      }

      return MigrationScanResult(
        totalFilesScanned: dartFiles.length,
        filesToMigrate: filesToMigrate,
        legacyUsages: legacyUsages,
        scanCompletedAt: DateTime.now(),
      );
    } on Exception catch (e) {
      throw MigrationException('Failed to scan project: $e');
    }
  }

  /// Migrate single file
  ///
  /// **Usage:**
  /// ```dart
  /// final result = await MigrationUtilities().migrateFile('lib/pages/settings_page.dart');
  /// if (result.success) {
  ///   print('File migrated successfully');
  /// }
  /// ```
  Future<MigrationResult> migrateFile(
    String filePath, {
    bool createBackup = true,
    bool validateAfterMigration = true,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw MigrationException('File not found: $filePath');
      }

      final originalContent = await file.readAsString();

      // Create backup if requested
      String? backupPath;
      if (createBackup) {
        backupPath = await _createBackup(filePath, originalContent);
      }

      // Apply migration transformations
      final migratedContent = _applyMigrationTransformations(originalContent);

      // Check if any changes were made
      if (migratedContent == originalContent) {
        return MigrationResult(
          filePath: filePath,
          success: true,
          changesApplied: 0,
          backupPath: backupPath,
          message: 'No migration needed - file already up to date',
        );
      }

      // Write migrated content
      await file.writeAsString(migratedContent);

      // Validate migration if requested
      if (validateAfterMigration) {
        final validationResult = await _validateMigration(filePath, migratedContent);
        if (!validationResult.isValid) {
          // Restore backup if validation fails
          if (backupPath != null) {
            await file.writeAsString(originalContent);
          }
          throw MigrationException('Migration validation failed: ${validationResult.errors.join(', ')}');
        }
      }

      final changesCount = _countChanges(originalContent, migratedContent);

      return MigrationResult(
        filePath: filePath,
        success: true,
        changesApplied: changesCount,
        backupPath: backupPath,
        message: 'Migration completed successfully with $changesCount changes',
      );
    } catch (e) {
      return MigrationResult(filePath: filePath, success: false, changesApplied: 0, message: 'Migration failed: $e');
    }
  }

  /// Migrate multiple files in batch
  ///
  /// **Usage:**
  /// ```dart
  /// final scanResult = await MigrationUtilities().scanProject();
  /// final batchResult = await MigrationUtilities().migrateBatch(
  ///   scanResult.filesToMigrate,
  ///   onProgress: (current, total) => print('Progress: $current/$total'),
  /// );
  /// ```
  Future<BatchMigrationResult> migrateBatch(
    List<String> filePaths, {
    bool createBackups = true,
    bool validateAfterMigration = true,
    Function(int current, int total)? onProgress,
  }) async {
    final results = <MigrationResult>[];
    var successCount = 0;
    var failureCount = 0;

    for (var i = 0; i < filePaths.length; i++) {
      final filePath = filePaths[i];

      // Report progress
      onProgress?.call(i + 1, filePaths.length);

      try {
        final result = await migrateFile(
          filePath,
          createBackup: createBackups,
          validateAfterMigration: validateAfterMigration,
        );

        results.add(result);

        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (e) {
        results.add(
          MigrationResult(filePath: filePath, success: false, changesApplied: 0, message: 'Batch migration failed: $e'),
        );
        failureCount++;
      }
    }

    return BatchMigrationResult(
      totalFiles: filePaths.length,
      successCount: successCount,
      failureCount: failureCount,
      results: results,
      completedAt: DateTime.now(),
    );
  }

  /// Generate migration script
  ///
  /// **Usage:**
  /// ```dart
  /// final script = await MigrationUtilities().generateMigrationScript();
  /// await File('migration_script.dart').writeAsString(script);
  /// ```
  Future<String> generateMigrationScript({String projectPath = '.', List<String> includePaths = const ['lib/']}) async {
    final scanResult = await scanProject(projectPath: projectPath, includePaths: includePaths);

    final scriptBuffer = StringBuffer();

    // Script header
    scriptBuffer.writeln('#!/usr/bin/env dart');
    scriptBuffer.writeln('');
    scriptBuffer.writeln('/// Automated Migration Script');
    scriptBuffer.writeln('/// Generated on: ${DateTime.now().toIso8601String()}');
    scriptBuffer.writeln('/// Total files to migrate: ${scanResult.filesToMigrate.length}');
    scriptBuffer.writeln('/// Total legacy usages: ${scanResult.legacyUsages.length}');
    scriptBuffer.writeln('');
    scriptBuffer.writeln("import 'dart:io';");
    scriptBuffer.writeln("import 'package:masajid_albahrain/core/notifications/migration/migration_utilities.dart';");
    scriptBuffer.writeln('');
    scriptBuffer.writeln('Future<void> main() async {');
    scriptBuffer.writeln('  print("Starting automated migration...");');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  final migrationUtils = MigrationUtilities();');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Files to migrate');
    scriptBuffer.writeln('  final filesToMigrate = [');

    for (final filePath in scanResult.filesToMigrate) {
      scriptBuffer.writeln("    '$filePath',");
    }

    scriptBuffer.writeln('  ];');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Perform batch migration');
    scriptBuffer.writeln('  final result = await migrationUtils.migrateBatch(');
    scriptBuffer.writeln('    filesToMigrate,');
    scriptBuffer.writeln('    createBackups: true,');
    scriptBuffer.writeln('    validateAfterMigration: true,');
    scriptBuffer.writeln('    onProgress: (current, total) {');
    scriptBuffer.writeln('      print("Progress: \$current/\$total");');
    scriptBuffer.writeln('    },');
    scriptBuffer.writeln('  );');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Print results');
    scriptBuffer.writeln('  print("Migration completed!");');
    scriptBuffer.writeln('  print("Success: \${result.successCount}");');
    scriptBuffer.writeln('  print("Failures: \${result.failureCount}");');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Print detailed results');
    scriptBuffer.writeln('  for (final migrationResult in result.results) {');
    scriptBuffer.writeln('    if (migrationResult.success) {');
    scriptBuffer.writeln('      print("✅ \${migrationResult.filePath}: \${migrationResult.changesApplied} changes");');
    scriptBuffer.writeln('    } else {');
    scriptBuffer.writeln('      print("❌ \${migrationResult.filePath}: \${migrationResult.message}");');
    scriptBuffer.writeln('    }');
    scriptBuffer.writeln('  }');
    scriptBuffer.writeln('}');

    return scriptBuffer.toString();
  }

  /// Rollback migration using backup
  ///
  /// **Usage:**
  /// ```dart
  /// await MigrationUtilities().rollbackMigration('lib/pages/settings_page.dart.backup');
  /// ```
  Future<bool> rollbackMigration(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw MigrationException('Backup file not found: $backupPath');
      }

      final originalPath = backupPath.replaceAll('.backup', '');
      final originalFile = File(originalPath);

      final backupContent = await backupFile.readAsString();
      await originalFile.writeAsString(backupContent);

      // Remove backup file after successful rollback
      await backupFile.delete();

      return true;
    } catch (e) {
      debugPrint('Rollback failed: $e');
      return false;
    }
  }

  /// Validate migration result
  Future<ValidationResult> validateMigration(String filePath) async {
    try {
      final file = File(filePath);
      final content = await file.readAsString();
      return await _validateMigration(filePath, content);
    } catch (e) {
      return ValidationResult(isValid: false, errors: ['Validation failed: $e']);
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Find all Dart files in the project
  Future<List<String>> _findDartFiles(String projectPath, List<String> includePaths, List<String> excludePaths) async {
    final dartFiles = <String>[];
    final projectDir = Directory(projectPath);

    await for (final entity in projectDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        final relativePath = entity.path.replaceFirst('$projectPath/', '');

        // Check if file should be included
        final shouldInclude = includePaths.any((path) => relativePath.startsWith(path));
        final shouldExclude = excludePaths.any((path) => relativePath.startsWith(path));

        if (shouldInclude && !shouldExclude) {
          dartFiles.add(entity.path);
        }
      }
    }

    return dartFiles;
  }

  /// Scan file for legacy provider usage
  List<LegacyUsage> _scanFileForLegacyUsage(String filePath, String content) {
    final usages = <LegacyUsage>[];
    final lines = content.split('\n');

    for (var i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;

      // Check for legacy provider patterns
      for (final pattern in _migrationPatterns.values) {
        // Check for import statements
        if (line.contains(pattern.oldImport.replaceAll("import '", '').replaceAll("';", ''))) {
          usages.add(
            LegacyUsage(
              filePath: filePath,
              lineNumber: lineNumber,
              type: LegacyUsageType.import,
              content: line.trim(),
              suggestedReplacement: pattern.newImport,
            ),
          );
        }

        // Check for provider usage
        for (final entry in pattern.providerReplacements.entries) {
          if (line.contains(entry.key)) {
            usages.add(
              LegacyUsage(
                filePath: filePath,
                lineNumber: lineNumber,
                type: LegacyUsageType.provider,
                content: line.trim(),
                suggestedReplacement: line.replaceAll(entry.key, entry.value),
              ),
            );
          }
        }

        // Check for method calls
        for (final entry in pattern.methodReplacements.entries) {
          if (line.contains(entry.key)) {
            usages.add(
              LegacyUsage(
                filePath: filePath,
                lineNumber: lineNumber,
                type: LegacyUsageType.method,
                content: line.trim(),
                suggestedReplacement: line.replaceAll(entry.key, entry.value),
              ),
            );
          }
        }
      }
    }

    return usages;
  }

  /// Apply migration transformations to content
  String _applyMigrationTransformations(String content) {
    var migratedContent = content;

    // Apply all migration patterns
    for (final pattern in _migrationPatterns.values) {
      // Replace imports
      migratedContent = migratedContent.replaceAll(pattern.oldImport, pattern.newImport);

      // Replace provider references
      for (final entry in pattern.providerReplacements.entries) {
        migratedContent = migratedContent.replaceAll(entry.key, entry.value);
      }

      // Replace method calls
      for (final entry in pattern.methodReplacements.entries) {
        migratedContent = migratedContent.replaceAll(entry.key, entry.value);
      }
    }

    return migratedContent;
  }

  /// Create backup of file
  Future<String> _createBackup(String filePath, String content) async {
    final backupPath = '$filePath.backup';
    final backupFile = File(backupPath);
    await backupFile.writeAsString(content);
    return backupPath;
  }

  /// Validate migration
  Future<ValidationResult> _validateMigration(String filePath, String content) async {
    final errors = <String>[];

    // Check for remaining legacy patterns
    for (final pattern in _migrationPatterns.values) {
      if (content.contains(pattern.oldImport)) {
        errors.add('Legacy import still present: ${pattern.oldImport}');
      }

      for (final oldProvider in pattern.providerReplacements.keys) {
        if (content.contains(oldProvider)) {
          errors.add('Legacy provider still present: $oldProvider');
        }
      }
    }

    // Check for syntax errors (basic validation)
    if (!content.contains('import ') && content.contains('ref.read(')) {
      errors.add('Missing import statements');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Count changes between original and migrated content
  int _countChanges(String original, String migrated) {
    final originalLines = original.split('\n');
    final migratedLines = migrated.split('\n');

    var changes = 0;
    final maxLines = originalLines.length > migratedLines.length ? originalLines.length : migratedLines.length;

    for (var i = 0; i < maxLines; i++) {
      final originalLine = i < originalLines.length ? originalLines[i] : '';
      final migratedLine = i < migratedLines.length ? migratedLines[i] : '';

      if (originalLine != migratedLine) {
        changes++;
      }
    }

    return changes;
  }
}

// ============================================================================
// DATA MODELS
// ============================================================================

/// Migration Pattern
class MigrationPattern {
  final String oldImport;
  final String newImport;
  final Map<String, String> providerReplacements;
  final Map<String, String> methodReplacements;

  const MigrationPattern({
    required this.oldImport,
    required this.newImport,
    required this.providerReplacements,
    required this.methodReplacements,
  });
}

/// Legacy Usage
class LegacyUsage {
  final String filePath;
  final int lineNumber;
  final LegacyUsageType type;
  final String content;
  final String suggestedReplacement;

  const LegacyUsage({
    required this.filePath,
    required this.lineNumber,
    required this.type,
    required this.content,
    required this.suggestedReplacement,
  });
}

/// Legacy Usage Type
enum LegacyUsageType { import, provider, method }

/// Migration Scan Result
class MigrationScanResult {
  final int totalFilesScanned;
  final List<String> filesToMigrate;
  final List<LegacyUsage> legacyUsages;
  final DateTime scanCompletedAt;

  const MigrationScanResult({
    required this.totalFilesScanned,
    required this.filesToMigrate,
    required this.legacyUsages,
    required this.scanCompletedAt,
  });
}

/// Migration Result
class MigrationResult {
  final String filePath;
  final bool success;
  final int changesApplied;
  final String? backupPath;
  final String message;

  const MigrationResult({
    required this.filePath,
    required this.success,
    required this.changesApplied,
    this.backupPath,
    required this.message,
  });
}

/// Batch Migration Result
class BatchMigrationResult {
  final int totalFiles;
  final int successCount;
  final int failureCount;
  final List<MigrationResult> results;
  final DateTime completedAt;

  const BatchMigrationResult({
    required this.totalFiles,
    required this.successCount,
    required this.failureCount,
    required this.results,
    required this.completedAt,
  });
}

/// Validation Result
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  const ValidationResult({required this.isValid, required this.errors});
}

/// Migration Exception
class MigrationException implements Exception {
  final String message;

  const MigrationException(this.message);

  @override
  String toString() => 'MigrationException: $message';
}

/// Migration CLI Tool
///
/// **Context7 MCP Command Line Interface:**
/// - Provides command-line interface for migration operations
/// - Supports batch processing with progress indicators
/// - Includes validation and rollback capabilities
/// - Generates comprehensive migration reports
/// - Follows Context7 MCP patterns for CLI design
class MigrationCLI {
  static final MigrationCLI _instance = MigrationCLI._internal();
  factory MigrationCLI() => _instance;
  MigrationCLI._internal();

  /// Run migration CLI
  ///
  /// **Usage:**
  /// ```bash
  /// dart run lib/core/notifications/migration/migration_cli.dart scan
  /// dart run lib/core/notifications/migration/migration_cli.dart migrate --all
  /// dart run lib/core/notifications/migration/migration_cli.dart rollback --file path/to/file.dart.backup
  /// ```
  Future<void> run(List<String> args) async {
    if (args.isEmpty) {
      _printUsage();
      return;
    }

    final command = args[0];
    final options = _parseOptions(args.skip(1).toList());

    try {
      switch (command) {
        case 'scan':
          await _runScan(options);
          break;
        case 'migrate':
          await _runMigrate(options);
          break;
        case 'rollback':
          await _runRollback(options);
          break;
        case 'validate':
          await _runValidate(options);
          break;
        case 'generate-script':
          await _runGenerateScript(options);
          break;
        case 'help':
          _printUsage();
          break;
        default:
          print('Unknown command: $command');
          _printUsage();
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Run scan command
  Future<void> _runScan(Map<String, String> options) async {
    print('🔍 Scanning project for legacy provider usage...');

    final migrationUtils = MigrationUtilities();
    final scanResult = await migrationUtils.scanProject(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
      excludePaths: options['exclude']?.split(',') ?? ['.dart_tool/', 'build/'],
    );

    print('\n📊 Scan Results:');
    print('  Total files scanned: ${scanResult.totalFilesScanned}');
    print('  Files to migrate: ${scanResult.filesToMigrate.length}');
    print('  Legacy usages found: ${scanResult.legacyUsages.length}');

    if (scanResult.legacyUsages.isNotEmpty) {
      print('\n📋 Legacy Usages:');
      for (final usage in scanResult.legacyUsages) {
        print('  ${usage.filePath}:${usage.lineNumber} - ${usage.type.name}');
        print('    Current: ${usage.content}');
        print('    Suggested: ${usage.suggestedReplacement}');
        print('');
      }
    }

    if (scanResult.filesToMigrate.isNotEmpty) {
      print('\n💡 Next steps:');
      print('  Run: dart run migration_cli.dart migrate --all');
      print('  Or: dart run migration_cli.dart generate-script');
    } else {
      print('\n✅ No migration needed - all files are up to date!');
    }
  }

  /// Run migrate command
  Future<void> _runMigrate(Map<String, String> options) async {
    final migrationUtils = MigrationUtilities();

    if (options.containsKey('all')) {
      print('🚀 Starting batch migration...');

      final scanResult = await migrationUtils.scanProject();
      if (scanResult.filesToMigrate.isEmpty) {
        print('✅ No files need migration!');
        return;
      }

      final batchResult = await migrationUtils.migrateBatch(
        scanResult.filesToMigrate,
        createBackups: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
        onProgress: (current, total) {
          print('Progress: $current/$total');
        },
      );

      print('\n📊 Migration Results:');
      print('  Total files: ${batchResult.totalFiles}');
      print('  Successful: ${batchResult.successCount}');
      print('  Failed: ${batchResult.failureCount}');

      for (final result in batchResult.results) {
        if (result.success) {
          print('  ✅ ${result.filePath}: ${result.changesApplied} changes');
        } else {
          print('  ❌ ${result.filePath}: ${result.message}');
        }
      }
    } else if (options.containsKey('file')) {
      final filePath = options['file']!;
      print('🔧 Migrating file: $filePath');

      final result = await migrationUtils.migrateFile(
        filePath,
        createBackup: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
      );

      if (result.success) {
        print('✅ Migration successful: ${result.changesApplied} changes');
        if (result.backupPath != null) {
          print('📁 Backup created: ${result.backupPath}');
        }
      } else {
        print('❌ Migration failed: ${result.message}');
      }
    } else {
      print('Error: Specify --all or --file <path>');
      _printUsage();
    }
  }

  /// Run rollback command
  Future<void> _runRollback(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('Error: Specify --file <backup-path>');
      return;
    }

    final backupPath = options['file']!;
    print('🔄 Rolling back migration: $backupPath');

    final migrationUtils = MigrationUtilities();
    final success = await migrationUtils.rollbackMigration(backupPath);

    if (success) {
      print('✅ Rollback successful');
    } else {
      print('❌ Rollback failed');
    }
  }

  /// Run validate command
  Future<void> _runValidate(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('Error: Specify --file <path>');
      return;
    }

    final filePath = options['file']!;
    print('🔍 Validating migration: $filePath');

    final migrationUtils = MigrationUtilities();
    final validationResult = await migrationUtils.validateMigration(filePath);

    if (validationResult.isValid) {
      print('✅ Validation successful');
    } else {
      print('❌ Validation failed:');
      for (final error in validationResult.errors) {
        print('  - $error');
      }
    }
  }

  /// Run generate script command
  Future<void> _runGenerateScript(Map<String, String> options) async {
    print('📝 Generating migration script...');

    final migrationUtils = MigrationUtilities();
    final script = await migrationUtils.generateMigrationScript(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
    );

    final scriptPath = options['output'] ?? 'migration_script.dart';
    await File(scriptPath).writeAsString(script);

    print('✅ Migration script generated: $scriptPath');
    print('💡 Run with: dart run $scriptPath');
  }

  /// Parse command line options
  Map<String, String> _parseOptions(List<String> args) {
    final options = <String, String>{};

    for (var i = 0; i < args.length; i++) {
      final arg = args[i];

      if (arg.startsWith('--')) {
        final key = arg.substring(2);

        if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
          options[key] = args[i + 1];
          i++; // Skip next argument as it's the value
        } else {
          options[key] = 'true'; // Flag without value
        }
      }
    }

    return options;
  }

  /// Print usage information
  void _printUsage() {
    print('''
Migration CLI Tool - Automated Provider Migration

Usage:
  dart run migration_cli.dart <command> [options]

Commands:
  scan                    Scan project for legacy provider usage
  migrate                 Migrate files to use unified providers
  rollback                Rollback migration using backup file
  validate                Validate migration result
  generate-script         Generate automated migration script
  help                    Show this help message

Options:
  --path <path>           Project path (default: .)
  --include <paths>       Include paths (comma-separated, default: lib/)
  --exclude <paths>       Exclude paths (comma-separated, default: .dart_tool/,build/)
  --file <path>           Specific file to migrate/rollback/validate
  --all                   Migrate all files
  --no-backup             Skip backup creation
  --no-validate           Skip validation after migration
  --output <path>         Output path for generated script (default: migration_script.dart)

Examples:
  dart run migration_cli.dart scan
  dart run migration_cli.dart migrate --all
  dart run migration_cli.dart migrate --file lib/pages/settings_page.dart
  dart run migration_cli.dart rollback --file lib/pages/settings_page.dart.backup
  dart run migration_cli.dart validate --file lib/pages/settings_page.dart
  dart run migration_cli.dart generate-script --output my_migration.dart

For more information, visit:
https://docs.masajid-albahrain.com/migration
''');
  }
}
