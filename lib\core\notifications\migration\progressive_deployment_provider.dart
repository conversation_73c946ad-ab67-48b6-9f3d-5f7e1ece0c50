// ignore_for_file: deprecated_member_use_from_same_package

import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import '../providers/unified_notification_provider.dart';
import 'feature_flag_system.dart';

part 'progressive_deployment_provider.g.dart';

/// Progressive Deployment Provider for Notification System Migration
///
/// **Context7 MCP Implementation:**
/// - Manages coexistence of unified and legacy providers
/// - Implements feature flag-based routing
/// - Provides compatibility layers for seamless migration
/// - Monitors performance and health metrics
/// - Supports emergency rollback capabilities
///
/// **Phase 1 Deployment Strategy:**
/// - Deploy unified providers alongside existing ones
/// - Use feature flags for gradual rollout (5% initial)
/// - Maintain backward compatibility through wrapper providers
/// - Monitor performance and error rates
/// - Enable emergency rollback if issues detected
///
/// **Usage:**
/// ```dart
/// final deployment = ref.watch(progressiveDeploymentProvider);
/// if (deployment.shouldUseUnifiedProvider) {
///   // Use unified notification provider
/// } else {
///   // Use legacy provider with compatibility layer
/// }
/// ```
@riverpod
class ProgressiveDeployment extends _$ProgressiveDeployment {
  @override
  Future<ProgressiveDeploymentState> build() async {
    try {
      AppLogger.info('ProgressiveDeployment: Initializing deployment state');

      // Initialize feature flag service
      final featureFlagService = FeatureFlagService();
      await featureFlagService.initialize();

      // Check if unified provider is enabled
      final unifiedEnabled = await featureFlagService.isEnabled(
        MigrationFeatureFlags.unifiedProviderEnabled,
      );

      // Check emergency rollback status
      final emergencyRollback = await featureFlagService.isEnabled(
        MigrationFeatureFlags.emergencyRollbackEnabled,
      );

      // Initialize deployment state
      final state = ProgressiveDeploymentState(
        phase: DeploymentPhase.phase1,
        unifiedProviderEnabled: unifiedEnabled && !emergencyRollback,
        legacyProviderActive: true, // Always keep legacy active in Phase 1
        compatibilityLayerActive: true,
        rolloutPercentage: unifiedEnabled ? 5 : 0,
        healthStatus: DeploymentHealthStatus.healthy,
        lastHealthCheck: DateTime.now(),
        metrics: DeploymentMetrics.initial(),
      );

      AppLogger.info(
        'ProgressiveDeployment: Initialized',
        context: {
          'phase': state.phase.name,
          'unifiedEnabled': state.unifiedProviderEnabled,
          'rolloutPercentage': state.rolloutPercentage,
        },
      );

      // Start health monitoring
      _startHealthMonitoring();

      return state;
    } catch (e, stackTrace) {
      AppLogger.error('ProgressiveDeployment: Initialization failed', e, stackTrace);
      
      // Return safe fallback state
      return ProgressiveDeploymentState(
        phase: DeploymentPhase.phase1,
        unifiedProviderEnabled: false,
        legacyProviderActive: true,
        compatibilityLayerActive: true,
        rolloutPercentage: 0,
        healthStatus: DeploymentHealthStatus.degraded,
        lastHealthCheck: DateTime.now(),
        metrics: DeploymentMetrics.initial(),
        error: e.toString(),
      );
    }
  }

  /// Check if unified provider should be used for current user
  bool shouldUseUnifiedProvider() {
    final currentState = state.value;
    if (currentState == null) return false;

    return currentState.unifiedProviderEnabled && 
           currentState.healthStatus != DeploymentHealthStatus.critical;
  }

  /// Check if legacy provider should be used
  bool shouldUseLegacyProvider() {
    final currentState = state.value;
    if (currentState == null) return true;

    return currentState.legacyProviderActive || !shouldUseUnifiedProvider();
  }

  /// Update rollout percentage
  Future<void> updateRolloutPercentage(int percentage) async {
    try {
      final currentState = state.value;
      if (currentState == null) return;

      // Update feature flag
      final featureFlagService = FeatureFlagService();
      await featureFlagService.setOverride(
        MigrationFeatureFlags.unifiedProviderEnabled,
        percentage > 0,
      );

      // Update state
      state = AsyncData(currentState.copyWith(
        rolloutPercentage: percentage,
        unifiedProviderEnabled: percentage > 0,
        lastHealthCheck: DateTime.now(),
      ));

      AppLogger.info(
        'ProgressiveDeployment: Updated rollout percentage',
        context: {'percentage': percentage},
      );
    } catch (e, stackTrace) {
      AppLogger.error('ProgressiveDeployment: Failed to update rollout', e, stackTrace);
    }
  }

  /// Trigger emergency rollback
  Future<void> emergencyRollback({required String reason}) async {
    try {
      AppLogger.warning(
        'ProgressiveDeployment: Emergency rollback triggered',
        context: {'reason': reason},
      );

      // Disable unified provider immediately
      final featureFlagService = FeatureFlagService();
      await featureFlagService.setOverride(
        MigrationFeatureFlags.unifiedProviderEnabled,
        false,
      );
      await featureFlagService.setOverride(
        MigrationFeatureFlags.emergencyRollbackEnabled,
        true,
      );

      // Update state
      final currentState = state.value;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(
          unifiedProviderEnabled: false,
          rolloutPercentage: 0,
          healthStatus: DeploymentHealthStatus.critical,
          lastHealthCheck: DateTime.now(),
          error: 'Emergency rollback: $reason',
        ));
      }

      AppLogger.error('ProgressiveDeployment: Emergency rollback completed');
    } catch (e, stackTrace) {
      AppLogger.error('ProgressiveDeployment: Emergency rollback failed', e, stackTrace);
    }
  }

  /// Start health monitoring
  void _startHealthMonitoring() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _performHealthCheck();
    });
  }

  /// Perform health check
  Future<void> _performHealthCheck() async {
    try {
      final currentState = state.value;
      if (currentState == null) return;

      // Check unified provider health if enabled
      var healthStatus = DeploymentHealthStatus.healthy;
      
      if (currentState.unifiedProviderEnabled) {
        // Check if unified provider is responding
        try {
          final unifiedManager = ref.read(unifiedNotificationManagerProvider.notifier);
          // Perform basic health check
          await unifiedManager.validateConfiguration();
          healthStatus = DeploymentHealthStatus.healthy;
        } catch (e) {
          AppLogger.warning('ProgressiveDeployment: Unified provider health check failed', context: {'error': e.toString()});
          healthStatus = DeploymentHealthStatus.degraded;
        }
      }

      // Update state with health status
      state = AsyncData(currentState.copyWith(
        healthStatus: healthStatus,
        lastHealthCheck: DateTime.now(),
      ));

    } catch (e, stackTrace) {
      AppLogger.error('ProgressiveDeployment: Health check failed', e, stackTrace);
    }
  }
}

/// Progressive Deployment State
class ProgressiveDeploymentState {
  final DeploymentPhase phase;
  final bool unifiedProviderEnabled;
  final bool legacyProviderActive;
  final bool compatibilityLayerActive;
  final int rolloutPercentage;
  final DeploymentHealthStatus healthStatus;
  final DateTime lastHealthCheck;
  final DeploymentMetrics metrics;
  final String? error;

  const ProgressiveDeploymentState({
    required this.phase,
    required this.unifiedProviderEnabled,
    required this.legacyProviderActive,
    required this.compatibilityLayerActive,
    required this.rolloutPercentage,
    required this.healthStatus,
    required this.lastHealthCheck,
    required this.metrics,
    this.error,
  });

  ProgressiveDeploymentState copyWith({
    DeploymentPhase? phase,
    bool? unifiedProviderEnabled,
    bool? legacyProviderActive,
    bool? compatibilityLayerActive,
    int? rolloutPercentage,
    DeploymentHealthStatus? healthStatus,
    DateTime? lastHealthCheck,
    DeploymentMetrics? metrics,
    String? error,
  }) {
    return ProgressiveDeploymentState(
      phase: phase ?? this.phase,
      unifiedProviderEnabled: unifiedProviderEnabled ?? this.unifiedProviderEnabled,
      legacyProviderActive: legacyProviderActive ?? this.legacyProviderActive,
      compatibilityLayerActive: compatibilityLayerActive ?? this.compatibilityLayerActive,
      rolloutPercentage: rolloutPercentage ?? this.rolloutPercentage,
      healthStatus: healthStatus ?? this.healthStatus,
      lastHealthCheck: lastHealthCheck ?? this.lastHealthCheck,
      metrics: metrics ?? this.metrics,
      error: error ?? this.error,
    );
  }
}

/// Deployment Phase
enum DeploymentPhase {
  phase1, // Deploy alongside existing
  phase2, // Migrate critical paths
  phase3, // Update remaining dependencies
  phase4, // Remove deprecated providers
  phase5, // Cleanup and optimization
}

/// Deployment Health Status
enum DeploymentHealthStatus {
  healthy,
  degraded,
  critical,
}

/// Deployment Metrics
class DeploymentMetrics {
  final int unifiedProviderUsage;
  final int legacyProviderUsage;
  final double errorRate;
  final Duration averageResponseTime;
  final int totalRequests;

  const DeploymentMetrics({
    required this.unifiedProviderUsage,
    required this.legacyProviderUsage,
    required this.errorRate,
    required this.averageResponseTime,
    required this.totalRequests,
  });

  factory DeploymentMetrics.initial() {
    return const DeploymentMetrics(
      unifiedProviderUsage: 0,
      legacyProviderUsage: 0,
      errorRate: 0.0,
      averageResponseTime: Duration.zero,
      totalRequests: 0,
    );
  }
}
