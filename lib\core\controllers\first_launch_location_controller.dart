// ignore_for_file: invalid_annotation_target

import 'dart:async';

import '../errors/app_error.dart';
import '../logging/app_logger.dart';
import '../models/location_data.dart';
import '../models/location_permission_models.dart';
import '../services/location/master_location_service.dart';
import '../utils/result.dart';

/// Context7 MCP: Initialization phases following Context7 MCP state machine pattern
enum InitializationPhase {
  idle, // Not started
  checkingPermissions, // Checking current permission status
  requestingPermissions, // Requesting location permissions
  initializingService, // Initializing location service
  waitingForFirstFix, // Waiting for first location fix
  completed, // Successfully initialized
  failed, // Initialization failed
}

/// Context7 MCP: First Launch Location Controller
///
/// Handles the complex initialization sequence for location services
/// on first app launch to prevent race conditions and ensure proper
/// location detection after permission granting.
///
/// **Context7 MCP Patterns:**
/// - Initialization Chain Pattern for sequential setup
/// - Retry Logic with Exponential Backoff
/// - State Machine for tracking initialization phases
/// - Comprehensive Error Handling and Recovery
class FirstLaunchLocationController {
  // ==================== State Management ====================

  InitializationPhase _currentPhase = InitializationPhase.idle;
  bool _isFirstLaunch = false;
  DateTime? _initializationStartTime;

  // ==================== Retry Configuration ====================

  static const int _maxRetryAttempts = 3;
  static const Duration _baseRetryDelay = Duration(seconds: 2);
  static const Duration _maxInitializationTimeout = Duration(minutes: 2);
  static const Duration _firstLocationTimeout = Duration(seconds: 30);

  int _retryAttempt = 0;
  Timer? _timeoutTimer;
  Timer? _retryTimer;

  // ==================== Completion Tracking ====================

  final Completer<Result<LocationData>> _initializationCompleter = Completer<Result<LocationData>>();
  StreamSubscription<LocationData>? _locationStreamSubscription;

  // ==================== Public API ====================

  /// Initialize location services for first launch
  /// Context7 MCP: Comprehensive first launch initialization
  Future<Result<LocationData>> initializeForFirstLaunch({
    bool isFirstLaunch = true,
    Duration timeout = _maxInitializationTimeout,
  }) async {
    if (_currentPhase != InitializationPhase.idle) {
      AppLogger.warning('FirstLaunchLocationController: Initialization already in progress');
      return _initializationCompleter.future;
    }

    _isFirstLaunch = isFirstLaunch;
    _initializationStartTime = DateTime.now();
    _retryAttempt = 0;

    AppLogger.info(
      'FirstLaunchLocationController: Starting first launch initialization '
      '(isFirstLaunch: $isFirstLaunch)',
    );

    // Context7 MCP: Set overall timeout
    _timeoutTimer = Timer(timeout, () {
      if (!_initializationCompleter.isCompleted) {
        _completeWithError('Initialization timeout after ${timeout.inSeconds} seconds');
      }
    });

    // Start the initialization sequence
    await _executeInitializationSequence();

    return _initializationCompleter.future;
  }

  /// Get current initialization phase
  InitializationPhase get currentPhase => _currentPhase;

  /// Check if initialization is in progress
  bool get isInitializing =>
      _currentPhase != InitializationPhase.idle &&
      _currentPhase != InitializationPhase.completed &&
      _currentPhase != InitializationPhase.failed;

  /// Get initialization progress (0.0 to 1.0)
  double get initializationProgress {
    switch (_currentPhase) {
      case InitializationPhase.idle:
        return 0.0;
      case InitializationPhase.checkingPermissions:
        return 0.2;
      case InitializationPhase.requestingPermissions:
        return 0.4;
      case InitializationPhase.initializingService:
        return 0.6;
      case InitializationPhase.waitingForFirstFix:
        return 0.8;
      case InitializationPhase.completed:
        return 1.0;
      case InitializationPhase.failed:
        return 0.0;
    }
  }

  // ==================== Private Implementation ====================

  /// Execute the complete initialization sequence
  /// Context7 MCP: Sequential initialization with proper error handling
  Future<void> _executeInitializationSequence() async {
    try {
      // Phase 1: Check current permissions
      _currentPhase = InitializationPhase.checkingPermissions;
      AppLogger.debug('FirstLaunchLocationController: Phase 1 - Checking permissions');

      final permissionResult = await _checkPermissions();
      if (permissionResult.isFailure) {
        await _handleRetryOrFail('Permission check failed: ${permissionResult.errorOrNull}');
        return;
      }

      // Phase 2: Request permissions if needed
      if ((permissionResult as Success<LocationPermissionStatus>).value != LocationPermissionStatus.granted) {
        _currentPhase = InitializationPhase.requestingPermissions;
        AppLogger.debug('FirstLaunchLocationController: Phase 2 - Requesting permissions');

        final requestResult = await _requestPermissions();
        if (requestResult.isFailure ||
            (requestResult as Success<LocationPermissionStatus>).value != LocationPermissionStatus.granted) {
          await _handleRetryOrFail('Permission request failed or denied');
          return;
        }
      }

      // Phase 3: Initialize location service
      _currentPhase = InitializationPhase.initializingService;
      AppLogger.debug('FirstLaunchLocationController: Phase 3 - Initializing service');

      final serviceResult = await _initializeLocationService();
      if (serviceResult.isFailure) {
        await _handleRetryOrFail('Service initialization failed: ${serviceResult.errorOrNull}');
        return;
      }

      // Phase 4: Wait for first location fix
      _currentPhase = InitializationPhase.waitingForFirstFix;
      AppLogger.debug('FirstLaunchLocationController: Phase 4 - Waiting for first location fix');

      await _waitForFirstLocationFix();
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'FirstLaunchLocationController: Unexpected error during initialization',
        error: e,
        stackTrace: stackTrace,
      );
      await _handleRetryOrFail('Unexpected error: $e');
    }
  }

  /// Check current permission status
  Future<Result<LocationPermissionStatus>> _checkPermissions() async {
    try {
      final service = MasterLocationService.instance;
      return await service.checkPermissionStatus();
    } on Exception catch (e) {
      return Result.failure(AppError.location('Failed to check permissions: $e'));
    }
  }

  /// Request location permissions
  Future<Result<LocationPermissionStatus>> _requestPermissions() async {
    try {
      final service = MasterLocationService.instance;
      return await service.requestPermissionProactively(
        isFirstLaunch: _isFirstLaunch,
        tutorialCompleted: false, // Assume tutorial not completed on first launch
        showRationale: true,
      );
    } on Exception catch (e) {
      return Result.failure(AppError.location('Failed to request permissions: $e'));
    }
  }

  /// Initialize the location service
  Future<Result<void>> _initializeLocationService() async {
    try {
      final service = MasterLocationService.instance;
      return await service.initialize();
    } on Exception catch (e) {
      return Result.failure(AppError.initialization('Failed to initialize service: $e'));
    }
  }

  /// Wait for first location fix with timeout
  Future<void> _waitForFirstLocationFix() async {
    try {
      final service = MasterLocationService.instance;

      // First try to get last known location
      final lastKnownResult = await service.getLastKnownLocation();
      if (lastKnownResult.isSuccess) {
        final location = (lastKnownResult as Success<LocationData?>).value;
        if (location != null) {
          AppLogger.info('FirstLaunchLocationController: Using last known location');
          _completeWithSuccess(location);
          return;
        }
      }

      // If no last known location, try to get current location
      final currentLocationResult = await service.getCurrentLocation(timeout: _firstLocationTimeout, useCache: false);

      if (currentLocationResult.isSuccess) {
        final location = (currentLocationResult as Success<LocationData>).value;
        AppLogger.info('FirstLaunchLocationController: Got current location');
        _completeWithSuccess(location);
        return;
      }

      // If current location fails, start location stream and wait
      AppLogger.debug('FirstLaunchLocationController: Starting location stream for first fix');
      final locationStream = service.getLocationStream();

      _locationStreamSubscription = locationStream
          .timeout(_firstLocationTimeout)
          .listen(
            (location) {
              AppLogger.info('FirstLaunchLocationController: Received first location from stream');
              _completeWithSuccess(location);
            },
            onError: (error) {
              AppLogger.error('FirstLaunchLocationController: Location stream error', error: error);
              _handleRetryOrFail('Location stream error: $error');
            },
          );
    } on Exception catch (e) {
      await _handleRetryOrFail('Failed to get first location fix: $e');
    }
  }

  /// Handle retry logic or final failure
  Future<void> _handleRetryOrFail(String errorMessage) async {
    _retryAttempt++;

    if (_retryAttempt < _maxRetryAttempts) {
      final retryDelay = Duration(
        milliseconds: (_baseRetryDelay.inMilliseconds * _retryAttempt).clamp(
          _baseRetryDelay.inMilliseconds,
          10000, // Max 10 seconds
        ),
      );

      AppLogger.warning(
        'FirstLaunchLocationController: Retry attempt $_retryAttempt/$_maxRetryAttempts '
        'after ${retryDelay.inSeconds}s. Error: $errorMessage',
      );

      _retryTimer = Timer(retryDelay, () {
        _currentPhase = InitializationPhase.idle;
        _executeInitializationSequence();
      });
    } else {
      _completeWithError('Max retry attempts reached. Last error: $errorMessage');
    }
  }

  /// Complete initialization with success
  void _completeWithSuccess(LocationData location) {
    if (_initializationCompleter.isCompleted) return;

    _currentPhase = InitializationPhase.completed;
    _cleanup();

    final duration = _initializationStartTime != null
        ? DateTime.now().difference(_initializationStartTime!)
        : Duration.zero;

    AppLogger.info(
      'FirstLaunchLocationController: Initialization completed successfully '
      'in ${duration.inSeconds}s',
    );

    _initializationCompleter.complete(Result.success(location));
  }

  /// Complete initialization with error
  void _completeWithError(String errorMessage) {
    if (_initializationCompleter.isCompleted) return;

    _currentPhase = InitializationPhase.failed;
    _cleanup();

    AppLogger.error('FirstLaunchLocationController: Initialization failed - $errorMessage');
    _initializationCompleter.complete(Result.failure(AppError.initialization(errorMessage)));
  }

  /// Cleanup resources
  void _cleanup() {
    _timeoutTimer?.cancel();
    _retryTimer?.cancel();
    _locationStreamSubscription?.cancel();

    _timeoutTimer = null;
    _retryTimer = null;
    _locationStreamSubscription = null;
  }

  /// Dispose of the controller
  void dispose() {
    _cleanup();
    if (!_initializationCompleter.isCompleted) {
      _initializationCompleter.complete(Result.failure(AppError.initialization('Controller disposed')));
    }
  }
}
